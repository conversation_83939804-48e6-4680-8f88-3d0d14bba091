#!/usr/bin/env -S deno run --allow-net --allow-read --allow-write

import { Application, Router, Context } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { crypto } from "https://deno.land/std@0.208.0/crypto/mod.ts";

// ===== 配置常量 =====
const CONVERSATION_CACHE_MAX_SIZE = 100;
const DEFAULT_REQUEST_TIMEOUT = 30000;
const PORT = 8000;

// ===== 全局变量 =====
let VALID_CLIENT_KEYS: Set<string> = new Set();
let VERTICAL_AUTH_TOKENS: string[] = [];
let currentVerticalTokenIndex = 0;
let modelsData: any = { data: [] };
let conversationCache: Map<string, any> = new Map();

// ===== 类型定义 =====
interface ChatMessage {
  role: string;
  content: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

interface ModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  vertical_model_id?: string;
  vertical_model_url?: string;
  output_reasoning_flag?: boolean;
  description?: string;
}

interface StreamChoice {
  delta: Record<string, any>;
  index: number;
  finish_reason?: string;
}

interface StreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: StreamChoice[];
}

interface ChatCompletionChoice {
  message: ChatMessage;
  index: number;
  finish_reason: string;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// ===== 工具函数 =====
function generateUUID(): string {
  return crypto.randomUUID();
}

function getCurrentTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

async function sha256Hash(text: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
}

function generateMessageFingerprint(role: string, content: string): Promise<string> {
  return sha256Hash(content).then(hash => `${role}:${hash}`);
}

// ===== 配置加载函数 =====
async function loadModels(): Promise<any> {
  try {
    const data = await Deno.readTextFile("models.json");
    const rawData = JSON.parse(data);
    
    let processedData: any;
    
    if ("data" in rawData) {
      processedData = rawData;
    } else if ("models" in rawData) {
      // 转换旧格式到新格式
      const processedModels: ModelInfo[] = [];
      for (const model of rawData.models) {
        const modelEntry: ModelInfo = {
          id: model.modelId || "",
          object: "model",
          created: getCurrentTimestamp(),
          owned_by: "vertical-studio",
          vertical_model_id: model.modelId || "",
          vertical_model_url: model.url || "",
          description: `${model.modelId || ""} (final answer only)`
        };
        
        // 创建带 -thinking 后缀的版本
        const thinkingEntry: ModelInfo = {
          ...modelEntry,
          id: `${modelEntry.id}-thinking`,
          description: `${modelEntry.id} (with thinking steps)`
        };
        
        processedModels.push(modelEntry);
        processedModels.push(thinkingEntry);
      }
      processedData = { data: processedModels };
    } else {
      processedData = { data: [] };
    }
    
    // 为每个模型设置内部标志
    for (const model of processedData.data) {
      model.output_reasoning_flag = model.id.endsWith("-thinking");
      if (!model.created || model.created === 0) {
        model.created = getCurrentTimestamp();
      }
    }
    
    return processedData;
  } catch (error) {
    console.error(`加载 models.json 时出错: ${error}`);
    return { data: [] };
  }
}

async function loadClientApiKeys(): Promise<void> {
  try {
    const data = await Deno.readTextFile("client_api_keys.json");
    const keys = JSON.parse(data);
    
    if (!Array.isArray(keys)) {
      console.warn("警告: client_api_keys.json 应包含密钥列表");
      VALID_CLIENT_KEYS = new Set();
      return;
    }
    
    VALID_CLIENT_KEYS = new Set(keys);
    if (VALID_CLIENT_KEYS.size === 0) {
      console.warn("警告: client_api_keys.json 为空");
    } else {
      console.log(`成功加载 ${VALID_CLIENT_KEYS.size} 个客户端 API 密钥`);
    }
  } catch (error) {
    if (error instanceof Deno.errors.NotFound) {
      console.error("错误: 未找到 client_api_keys.json");
    } else {
      console.error(`加载 client_api_keys.json 时出错: ${error}`);
    }
    VALID_CLIENT_KEYS = new Set();
  }
}

async function loadVerticalAuthTokens(): Promise<void> {
  try {
    const data = await Deno.readTextFile("vertical.txt");
    const lines = data.split('\n');
    
    const loadedTokens: string[] = [];
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine) {
        const parts = trimmedLine.split("----");
        if (parts.length >= 1) {
          loadedTokens.push(parts[0]);
        }
      }
    }
    
    VERTICAL_AUTH_TOKENS = loadedTokens;
    if (VERTICAL_AUTH_TOKENS.length === 0) {
      console.warn("警告: vertical.txt 中未找到有效令牌");
    } else {
      console.log(`成功加载 ${VERTICAL_AUTH_TOKENS.length} 个 Vertical 认证令牌`);
    }
  } catch (error) {
    if (error instanceof Deno.errors.NotFound) {
      console.error("错误: 未找到 vertical.txt");
    } else {
      console.error(`加载 vertical.txt 时出错: ${error}`);
    }
    VERTICAL_AUTH_TOKENS = [];
  }
}

function getModelItem(modelId: string): ModelInfo | null {
  for (const model of modelsData.data) {
    if (model.id === modelId) {
      return model;
    }
  }
  return null;
}

function getNextVerticalAuthToken(): string {
  if (VERTICAL_AUTH_TOKENS.length === 0) {
    throw new Error("服务不可用: 未配置 Vertical 认证令牌");
  }
  
  const tokenToUse = VERTICAL_AUTH_TOKENS[currentVerticalTokenIndex];
  currentVerticalTokenIndex = (currentVerticalTokenIndex + 1) % VERTICAL_AUTH_TOKENS.length;
  return tokenToUse;
}

// ===== 认证中间件 =====
function extractBearerToken(authHeader: string | undefined): string | null {
  if (!authHeader) return null;
  const match = authHeader.match(/^Bearer\s+(.+)$/i);
  return match ? match[1] : null;
}

async function authenticateClient(ctx: Context, next: () => Promise<unknown>): Promise<void> {
  if (VALID_CLIENT_KEYS.size === 0) {
    ctx.response.status = 503;
    ctx.response.body = { detail: "服务不可用: 未配置客户端 API 密钥" };
    return;
  }
  
  const authHeader = ctx.request.headers.get("Authorization");
  const token = extractBearerToken(authHeader);
  
  if (!token) {
    ctx.response.status = 401;
    ctx.response.headers.set("WWW-Authenticate", "Bearer");
    ctx.response.body = { detail: "需要在 Authorization header 中提供 API 密钥" };
    return;
  }
  
  if (!VALID_CLIENT_KEYS.has(token)) {
    ctx.response.status = 403;
    ctx.response.body = { detail: "无效的客户端 API 密钥" };
    return;
  }
  
  await next();
}

// ===== VerticalApiClient 实现 =====
class VerticalApiClient {
  constructor() {}

  async getChatId(modelUrl: string, authToken: string): Promise<string | null> {
    try {
      const response = await fetch(modelUrl, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "new_chat"
        })
      });

      if (!response.ok) {
        console.error(`获取 chat_id 失败: ${response.status} ${response.statusText}`);
        return null;
      }

      const data = await response.json();
      return data.chat_id || data.id || null;
    } catch (error) {
      console.error(`获取 chat_id 时出错: ${error}`);
      return null;
    }
  }

  async* sendMessageStream(
    authToken: string,
    chatId: string,
    message: string,
    modelId: string,
    outputReasoning: boolean,
    systemPrompt: string
  ): AsyncGenerator<string, void, unknown> {
    try {
      const payload = {
        chat_id: chatId,
        message: message,
        model_id: modelId,
        system_prompt: systemPrompt,
        output_reasoning: outputReasoning,
        stream: true
      };

      const response = await fetch("https://api.vertical.ai/v1/chat/stream", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        yield `error:${JSON.stringify({ message: `HTTP ${response.status}: ${response.statusText}` })}`;
        return;
      }

      if (!response.body) {
        yield `error:${JSON.stringify({ message: "No response body" })}`;
        return;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine) {
              yield trimmedLine;
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      yield `error:${JSON.stringify({ message: `Network error: ${error.message}` })}`;
    }
  }
}

// ===== JSON 解析工具函数 =====
function parseJsonStringContent(line: string, prefixLen: number, suffixLen: number): string {
  const contentSegment = line.substring(prefixLen, line.length + suffixLen);
  try {
    // 将提取的片段视为JSON字符串的值进行解析，以处理所有标准转义序列
    return JSON.parse(`"${contentSegment}"`);
  } catch (error) {
    // 如果解析失败，回退到手动替换
    console.warn(`警告: parseJsonStringContent 中 JSONDecodeError，回退处理。原始片段: ${contentSegment.substring(0, 100)}...`);
    let tempContent = contentSegment.replace(/\\"/g, '"'); // 处理 \" -> "
    tempContent = tempContent.replace(/\\n/g, '\n');       // 处理 \n -> 换行符
    tempContent = tempContent.replace(/\\t/g, '\t');       // 处理 \t -> 制表符
    return tempContent;
  }
}

// ===== 对话缓存管理 =====
function updateConversationCache(
  isNewCachedConv: boolean,
  verticalChatIdForCache: string,
  matchedConvIdForCacheUpdate: string | null,
  originalRequestMessages: ChatMessage[],
  fullAssistantReplyStr: string,
  systemPromptHashForCache: number,
  modelUrlForCache: string
): void {
  if (isNewCachedConv) {
    // 新对话，创建缓存条目
    const newInternalId = generateUUID();
    const currentFingerprints: Promise<string>[] = originalRequestMessages.map(msg =>
      generateMessageFingerprint(msg.role, msg.content)
    );

    // 添加助手回复的指纹
    currentFingerprints.push(generateMessageFingerprint("assistant", fullAssistantReplyStr));

    Promise.all(currentFingerprints).then(fingerprints => {
      conversationCache.set(newInternalId, {
        vertical_chat_id: verticalChatIdForCache,
        vertical_model_url: modelUrlForCache,
        system_prompt_hash: systemPromptHashForCache,
        message_fingerprints: fingerprints,
        last_seen: Date.now()
      });

      // LRU 驱逐
      if (conversationCache.size > CONVERSATION_CACHE_MAX_SIZE) {
        const firstKey = conversationCache.keys().next().value;
        conversationCache.delete(firstKey);
      }
    });
  } else if (matchedConvIdForCacheUpdate) {
    // 更新现有对话
    const cachedItem = conversationCache.get(matchedConvIdForCacheUpdate);
    if (cachedItem) {
      // 添加最新用户消息的指纹（如果还未添加）
      if (originalRequestMessages.length > 0) {
        const lastUserMsg = originalRequestMessages[originalRequestMessages.length - 1];
        generateMessageFingerprint(lastUserMsg.role, lastUserMsg.content).then(lastUserFingerprint => {
          // 检查是否已经存在，避免重复
          if (!cachedItem.message_fingerprints ||
              cachedItem.message_fingerprints[cachedItem.message_fingerprints.length - 1] !== lastUserFingerprint) {
            cachedItem.message_fingerprints.push(lastUserFingerprint);
          }

          // 添加助手回复的指纹
          generateMessageFingerprint("assistant", fullAssistantReplyStr).then(assistantFingerprint => {
            cachedItem.message_fingerprints.push(assistantFingerprint);
            cachedItem.last_seen = Date.now();
          });
        });
      }
    }
  }
}

// ===== 流式响应适配器 =====
async function* openaiStreamAdapter(
  apiStreamGenerator: AsyncGenerator<string, void, unknown>,
  modelNameForResponse: string,
  reasoningRequested: boolean,
  verticalChatIdForCache: string,
  isNewCachedConv: boolean,
  matchedConvIdForCacheUpdate: string | null,
  originalRequestMessages: ChatMessage[],
  systemPromptHashForCache: number,
  modelUrlForCache: string
): AsyncGenerator<string, void, unknown> {
  const fullAssistantReplyParts: string[] = [];
  const streamId = `chatcmpl-${generateUUID().replace(/-/g, '')}`;

  try {
    let firstChunkSent = false;

    for await (const line of apiStreamGenerator) {
      if (line.startsWith("error:")) {
        // 处理错误
        try {
          const errorData = JSON.parse(line.substring(6));
          const errorMsg = errorData.message || "Unknown error";

          const errorResp: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created: getCurrentTimestamp(),
            model: modelNameForResponse,
            choices: [{
              delta: { role: "assistant", content: `错误: ${errorMsg}` },
              index: 0,
              finish_reason: "stop"
            }]
          };
          yield `data: ${JSON.stringify(errorResp)}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        } catch {
          const errorMsg = "Unknown error from Vertical API";
          const errorResp: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created: getCurrentTimestamp(),
            model: modelNameForResponse,
            choices: [{
              delta: { role: "assistant", content: `错误: ${errorMsg}` },
              index: 0,
              finish_reason: "stop"
            }]
          };
          yield `data: ${JSON.stringify(errorResp)}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        }
      }

      let deltaPayload: Record<string, any> | null = null;

      // 解析 Vertical API 的响应格式
      if (line.startsWith('0:"') && line.endsWith('"')) {
        // 主要内容
        const finalContent = parseJsonStringContent(line, 3, -1);
        if (!firstChunkSent) {
          deltaPayload = { role: "assistant", content: finalContent };
        } else {
          deltaPayload = { content: finalContent };
        }
        fullAssistantReplyParts.push(finalContent);
      } else if (reasoningRequested && line.startsWith('g:"') && line.endsWith('"')) {
        // 推理内容（仅当请求时才包含）
        const thinkingContent = parseJsonStringContent(line, 3, -1);
        // 为缓存添加带前缀的思考内容，但在SSE事件中分离reasoning_content
        fullAssistantReplyParts.push(`[Thinking]: ${thinkingContent}`);
        if (!firstChunkSent) {
          deltaPayload = { role: "assistant", reasoning_content: thinkingContent };
        } else {
          deltaPayload = { reasoning_content: thinkingContent };
        }
      } else if (line.startsWith('d:')) {
        // 可能是结束信号
        try {
          const eventData = JSON.parse(line.substring(2));
          if (eventData.type === "done" || eventData.type === "DONE") {
            // 发送最终的 finish_reason
            const finalResp: StreamResponse = {
              id: streamId,
              object: "chat.completion.chunk",
              created: getCurrentTimestamp(),
              model: modelNameForResponse,
              choices: [{
                delta: {},
                index: 0,
                finish_reason: "stop"
              }]
            };
            yield `data: ${JSON.stringify(finalResp)}\n\n`;
            break;
          }
        } catch {
          // 忽略解析错误
        }
      }

      // 如果有内容要发送
      if (deltaPayload) {
        const streamResp: StreamResponse = {
          id: streamId,
          object: "chat.completion.chunk",
          created: getCurrentTimestamp(),
          model: modelNameForResponse,
          choices: [{
            delta: deltaPayload,
            index: 0
          }]
        };

        if (!firstChunkSent) {
          firstChunkSent = true;
        }

        yield `data: ${JSON.stringify(streamResp)}\n\n`;
      }
    }

    // 更新缓存
    const fullAssistantReply = fullAssistantReplyParts.join("\n");
    updateConversationCache(
      isNewCachedConv,
      verticalChatIdForCache,
      matchedConvIdForCacheUpdate,
      originalRequestMessages,
      fullAssistantReply,
      systemPromptHashForCache,
      modelUrlForCache
    );

    // 发送结束标记
    yield "data: [DONE]\n\n";

  } catch (error) {
    console.error(`流式适配器错误: ${error}`);
    const errorResp: StreamResponse = {
      id: streamId,
      object: "chat.completion.chunk",
      created: getCurrentTimestamp(),
      model: modelNameForResponse,
      choices: [{
        delta: { role: "assistant", content: `内部错误: ${error}` },
        index: 0,
        finish_reason: "stop"
      }]
    };
    yield `data: ${JSON.stringify(errorResp)}\n\n`;
    yield "data: [DONE]\n\n";
  }
}

// ===== 聚合流式响应用于非流式返回 =====
async function aggregateStreamForNonStreamResponse(
  openaiSseStream: AsyncGenerator<string, void, unknown>,
  modelName: string
): Promise<ChatCompletionResponse> {
  const contentParts: string[] = [];
  const reasoningParts: string[] = [];

  for await (const sseLine of openaiSseStream) {
    if (sseLine.startsWith("data: ") && sseLine !== "data: [DONE]\n\n") {
      try {
        const data = JSON.parse(sseLine.substring(6).trim());
        if (data.choices && data.choices.length > 0) {
          const delta = data.choices[0].delta || {};
          if (delta.content) {
            contentParts.push(delta.content);
          } else if (delta.reasoning_content) {
            reasoningParts.push(delta.reasoning_content);
          }
        }
      } catch {
        // 忽略解析错误
      }
    }
  }

  // 组合最终内容，如果有推理内容则添加
  const combinedParts: string[] = [];
  if (reasoningParts.length > 0) {
    for (const part of reasoningParts) {
      combinedParts.push(`[Thinking]: ${part}`);
    }
  }

  combinedParts.push(...contentParts);
  const fullContent = combinedParts.join("");

  return {
    id: `chatcmpl-${generateUUID().replace(/-/g, '')}`,
    object: "chat.completion",
    created: getCurrentTimestamp(),
    model: modelName,
    choices: [{
      message: { role: "assistant", content: fullContent },
      index: 0,
      finish_reason: "stop"
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}

// ===== 初始化函数 =====
async function initializeServer(): Promise<void> {
  modelsData = await loadModels();
  await loadClientApiKeys();
  await loadVerticalAuthTokens();
  console.log("Vertical OpenAI Compatible API 服务器已启动");
}

// ===== 路由设置 =====
const router = new Router();
const verticalApiClient = new VerticalApiClient();

// 模型列表端点
router.get("/v1/models", authenticateClient, (ctx: Context) => {
  const modelList: ModelInfo[] = [];
  for (const model of modelsData.data) {
    modelList.push({
      id: model.id || "",
      object: "model",
      created: model.created || getCurrentTimestamp(),
      owned_by: model.owned_by || "vertical-studio"
    });
  }

  ctx.response.body = {
    object: "list",
    data: modelList
  };
});

// 健康检查端点
router.get("/health", (ctx: Context) => {
  ctx.response.body = {
    status: "ok",
    service: "vertical-openai-adapter",
    models: modelsData.data.length,
    timestamp: getCurrentTimestamp()
  };
});

// 主要聊天完成端点
router.post("/v1/chat/completions", authenticateClient, async (ctx: Context) => {
  try {
    const request: ChatCompletionRequest = await ctx.request.body({ type: "json" }).value;

    // 获取模型配置
    const modelConfig = getModelItem(request.model);
    if (!modelConfig) {
      ctx.response.status = 404;
      ctx.response.body = { detail: `模型 ${request.model} 未找到` };
      return;
    }

    // 获取认证令牌
    const authToken = getNextVerticalAuthToken();

    // 从模型配置中提取信息
    const verticalModelId = modelConfig.vertical_model_id;
    const verticalModelUrl = modelConfig.vertical_model_url;
    const outputReasoningActive = modelConfig.output_reasoning_flag || false;

    if (!verticalModelId || !verticalModelUrl) {
      ctx.response.status = 500;
      ctx.response.body = { detail: "模型配置不完整" };
      return;
    }

    // 提取系统提示和用户消息
    let currentSystemPromptStr = "";
    let latestUserMessageContent = "";

    for (const msg of request.messages) {
      if (msg.role === "system") {
        currentSystemPromptStr += msg.content + "\n";
      } else if (msg.role === "user") {
        latestUserMessageContent = msg.content;
      }
    }

    currentSystemPromptStr = currentSystemPromptStr.trim();

    if (!latestUserMessageContent) {
      ctx.response.status = 400;
      ctx.response.body = { detail: "请求中未找到用户消息" };
      return;
    }

    // 生成系统提示哈希
    const currentSystemPromptHash = await sha256Hash(currentSystemPromptStr).then(hash => hash.length);

    // 简化的缓存查找（在实际应用中可以实现更复杂的缓存逻辑）
    const isNewCachedConversation = true; // 简化实现
    const matchedConvId = null;

    // 新对话
    const newChatId = await verticalApiClient.getChatId(verticalModelUrl, authToken);

    if (!newChatId) {
      ctx.response.status = 500;
      ctx.response.body = { detail: "无法从 Vertical API 获取 chat_id" };
      return;
    }

    console.log(`创建新对话 chat_id: ${newChatId}`);

    // 为新对话构造完整历史
    const historyParts: string[] = [];
    for (const msg of request.messages) {
      if (msg.role === "user") {
        historyParts.push(`User: ${msg.content}`);
      } else if (msg.role === "assistant") {
        historyParts.push(`Assistant: ${msg.content}`);
      }
    }

    const messageToSendToVertical = historyParts.length > 0 ? historyParts.join("\n") : latestUserMessageContent;

    // 调用 Vertical API
    const apiStreamGenerator = verticalApiClient.sendMessageStream(
      authToken,
      newChatId,
      messageToSendToVertical,
      verticalModelId,
      outputReasoningActive,
      currentSystemPromptStr
    );

    // 创建 OpenAI 格式的流
    const openaiSseStream = openaiStreamAdapter(
      apiStreamGenerator,
      request.model,
      outputReasoningActive,
      newChatId,
      isNewCachedConversation,
      matchedConvId,
      request.messages,
      currentSystemPromptHash,
      verticalModelUrl
    );

    // 返回流式或非流式响应
    if (request.stream) {
      ctx.response.headers.set("Content-Type", "text/event-stream");
      ctx.response.headers.set("Cache-Control", "no-cache");
      ctx.response.headers.set("Connection", "keep-alive");

      const body = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of openaiSseStream) {
              controller.enqueue(new TextEncoder().encode(chunk));
            }
          } catch (error) {
            console.error("Stream error:", error);
          } finally {
            controller.close();
          }
        }
      });

      ctx.response.body = body;
    } else {
      const response = await aggregateStreamForNonStreamResponse(openaiSseStream, request.model);
      ctx.response.body = response;
    }

  } catch (error) {
    console.error("Chat completions error:", error);
    ctx.response.status = 500;
    ctx.response.body = { detail: `内部服务器错误: ${error.message}` };
  }
});

// ===== 应用程序设置 =====
const app = new Application();

// 错误处理中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  try {
    await next();
  } catch (err) {
    console.error("Application error:", err);
    ctx.response.status = 500;
    ctx.response.body = { detail: "Internal server error" };
  }
});

// CORS 中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  ctx.response.headers.set("Access-Control-Allow-Origin", "*");
  ctx.response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  ctx.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 200;
    return;
  }

  await next();
});

// 添加路由
app.use(router.routes());
app.use(router.allowedMethods());

// ===== 启动服务器 =====
async function main() {
  try {
    // 初始化配置
    await initializeServer();

    // 创建示例配置文件（如果不存在）
    try {
      await Deno.readTextFile("client_api_keys.json");
    } catch (error) {
      if (error instanceof Deno.errors.NotFound) {
        await Deno.writeTextFile(
          "client_api_keys.json",
          JSON.stringify(["sk-your-custom-key-here"], null, 2)
        );
        console.log("已创建示例 client_api_keys.json 文件");
      }
    }

    console.log("正在启动 Vertical OpenAI Compatible API 服务器...");
    console.log("端点:");
    console.log("  GET  /v1/models");
    console.log("  POST /v1/chat/completions");
    console.log("  GET  /health");
    console.log(`\n在 Authorization header 中使用客户端 API 密钥 (Bearer sk-xxx)`);
    console.log(`服务器运行在 http://localhost:${PORT}`);

    await app.listen({ port: PORT });
  } catch (error) {
    console.error("启动服务器时出错:", error);
    Deno.exit(1);
  }
}

// 如果直接运行此文件，启动服务器
if (import.meta.main) {
  main();
}
